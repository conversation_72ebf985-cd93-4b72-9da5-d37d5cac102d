# 最终执行方案 - 可直接实施

## 🎯 硬性指标确认

- **滚动FPS** ≥ 55
- **输入→预览延迟** P95 < 150ms
- **单次长任务** < 50ms
- **预览区DOM子节点** ≤ 2500
- **L档内存稳定** 2分钟滚动不持续上升≥10%

## 📋 Milestone执行计划

### 🚀 M1（T+2天）- 立即开始

#### 核心任务清单
- [ ] **稳定段ID**：`id = fastHash32(plainText) + ":" + startOffset`
- [ ] **输入防抖**：rAF + 120ms防抖
- [ ] **增量DOM**：keyed-diff，禁止`innerHTML=''`
- [ ] **滚动恢复**：移除强制scrollTop
- [ ] **性能埋点**：LongTasks + performance.mark/measure
- [ ] **安全基线**：消毒、外链、图片懒加载

#### 具体实现任务

**1. 哈希算法选择和实现**
```typescript
// 选择：MurmurHash3 x86_32（性能更好）
import { murmurHash3 } from 'murmurhash3js-revisited';

const generateStableId = (plainText: string, startOffset: number): string => {
  const hash = murmurHash3.x86.hash32(plainText);
  return `${hash}:${startOffset}`;
};

// 在段对象中缓存hash，避免重复计算
interface ContentSegment {
  id: string;
  content: string;
  hash: number; // 缓存的哈希值
  startOffset: number;
  element?: HTMLElement;
}
```

**2. keyed-diff完整实现**
```typescript
const updateDOMKeyed = (
  container: HTMLElement,
  oldSegments: ContentSegment[],
  newSegments: ContentSegment[]
) => {
  // O(n)单趟算法
  // 支持add/remove/update/move
  // 批量插入/删除优化
  // 一次性replaceChildren(fragment)
  // 避免多次布局抖动
};
```

**3. 防抖旁路机制**
```typescript
const LARGE_CHANGE_THRESHOLD = {
  lines: 200,
  bytes: 64 * 1024 // 64KB
};

const shouldBypassDebounce = (change: ChangeInfo): boolean => {
  return change.lines >= LARGE_CHANGE_THRESHOLD.lines ||
         change.bytes >= LARGE_CHANGE_THRESHOLD.bytes;
};
```

**4. 调试面板实现**
```typescript
// 固定事件名
const PERF_EVENTS = {
  EDIT_START: 'edit-start',
  PARSE_END: 'parse-end',
  PREVIEW_COMMIT: 'preview-commit'
};

// 性能监控面板
interface PerformanceMetrics {
  fps: number;
  p95Delay: number;
  longTaskCount: number;
  maxLongTask: number;
  domNodeCount: number;
  memoryUsage: number[];
}
```

**5. 安全基线**
```typescript
// 消毒策略
import rehypeSanitize from 'rehype-sanitize';

// 外链策略
const processExternalLinks = (html: string): string => {
  return html.replace(
    /<a\s+href="https?:\/\/[^"]*"/g,
    '$& rel="noopener noreferrer"'
  );
};

// 图片懒加载
const processImages = (html: string): string => {
  return html.replace(
    /<img\s+src="([^"]*)"([^>]*)>/g,
    '<img loading="lazy" src="$1" width="auto" height="auto"$2>'
  );
};
```

### 🔧 M2（T+1周）- 结构优化

#### 核心任务清单
- [ ] **局部重分段**：基于update.changes的增量处理
- [ ] **语义锚点滚动**：heading + 段内偏移
- [ ] **自适应防抖**：动态调整防抖时间
- [ ] **TOC一次产出**：解析时直接生成目录

#### 具体实现任务

**1. BoundaryIndex实现**
```typescript
class BoundaryIndex {
  private lineStart: Uint32Array;
  
  constructor(content: string) {
    this.lineStart = this.buildLineStartArray(content);
  }
  
  toOffset(pos: { line: number; col: number }): number {
    return this.lineStart[pos.line] + pos.col;
  }
  
  toPos(offset: number): { line: number; col: number } {
    // 二分查找实现
  }
  
  applyChanges(changes: ChangeDesc): void {
    // 增量更新lineStart数组
    // 变更之后的行统一加上Δ
  }
}
```

**2. 局部重分段三钩子**
```typescript
const calculateAffectedLineRange = (changes: ChangeDesc, oldContent: string) => {
  // 计算受影响的行范围
};

const findAffectedSegments = (segments: ContentSegment[], lineRange: Range) => {
  // 二分查找受影响段索引，外扩1段缓冲
};

const splitContentInRange = (content: string, start: number, end: number) => {
  // 对指定范围重新分段（heading/paragraph/code fence）
};
```

**3. 自适应防抖公式**
```typescript
const calculateAdaptiveDebounce = (totalLines: number): number => {
  return Math.max(80, Math.min(200, 80 + 0.004 * totalLines));
};

// 立即刷新条件
const shouldImmediateRefresh = (inputIdleTime: number): boolean => {
  return inputIdleTime >= 300; // 300ms
};
```

### ⚡ M3（T+1-2周）- 深度优化

#### 核心任务清单
- [ ] **Worker解析**：unified管线迁移
- [ ] **Worker协议**：rev版本控制
- [ ] **视口内高亮**：大代码块延迟高亮
- [ ] **虚拟滚动**：数据驱动启用

#### 具体实现任务

**1. Worker协议设计**
```typescript
// 主线程 → Worker
interface WorkerRequest {
  rev: number; // 递增版本号
  segments: Array<{
    id: string;
    content: string;
  }>;
}

// Worker → 主线程
interface WorkerResponse {
  rev: number; // 对应的版本号
  results: Array<{
    id: string;
    html: string;
    metadata?: {
      toc?: TOCItem[];
      headings?: HeadingInfo[];
    };
  }>;
}
```

**2. 虚拟滚动启用条件**
```typescript
const shouldEnableVirtualScrolling = (
  segmentCount: number,
  domNodeCount: number,
  currentFPS: number
): boolean => {
  return segmentCount > 3000 || 
         (domNodeCount > 2500 && currentFPS < 50);
};
```

## 🚨 Blocking事项（M1前必须完成）

### 1. 哈希算法确定 ✅
**决定**：使用MurmurHash3 x86_32
```bash
npm install murmurhash3js-revisited
```

### 2. keyed-diff覆盖面 ✅
**要求**：
- 实现move操作的O(n)算法
- 批量插入/删除优化
- 一次性replaceChildren(fragment)
- 避免多次布局抖动

### 3. 防抖旁路定义 ✅
**阈值**：
- 行数 ≥ 200
- 字节 ≥ 64KB
- blur/保存事件立即刷新

### 4. 调试面板事件名 ✅
**固定事件名**：
- `edit-start`
- `parse-end`
- `preview-commit`

### 5. 安全与渲染纯度 ✅
**要求**：
- rehype-sanitize消毒
- 外链添加rel="noopener noreferrer"
- 图片懒加载并给出尺寸

## 📊 验收标准

### 测试文档档位
- **S档**：2-3k行
- **M档**：8-10k行
- **L档**：18-25k行（含大JSON/表格/列表）

### 自动化测试用例
```typescript
// 解析快照比对
test('parsing accuracy', () => {
  const result = parseMarkdown(fixedInput);
  expect(result).toMatchSnapshot();
});

// 滚动稳定性
test('scroll stability', async () => {
  await page.scroll(2000);
  const visibleSegmentId = await getVisibleSegmentId();
  expect(Math.abs(visibleSegmentId - expectedId)).toBeLessThanOrEqual(1);
});

// 大纲跳转
test('outline navigation', async () => {
  await clickOutlineItem('heading-1');
  await waitForImagesLoaded();
  const currentHeading = await getCurrentHeading();
  expect(currentHeading).toBe('heading-1');
});

// 长任务统计
test('long tasks monitoring', () => {
  const longTasks = collectLongTasks(5000); // 5s窗口
  expect(longTasks.every(task => task.duration < 50)).toBe(true);
});
```

### 特性开关配置
```typescript
const featureFlags = {
  worker: true,
  incrementalSegments: true,
  keyedDiff: true,
  adaptiveDebounce: true,
  virtualList: false // 按需启用
};
```

## 🎯 立即行动计划

### 今天开始（M1第1天）
1. **安装依赖**：murmurhash3js-revisited, rehype-sanitize
2. **实现稳定段ID**：哈希函数 + startOffset计算
3. **实现基础防抖**：120ms固定防抖
4. **开始keyed-diff**：基础框架搭建

### 明天完成（M1第2天）
1. **完成keyed-diff**：move操作 + 批量优化
2. **性能埋点**：LongTasks监控 + 调试面板
3. **安全基线**：消毒 + 外链 + 图片处理
4. **M1验收测试**：S/M档性能指标测试

## ✅ 准备状态确认

- ✅ **技术方案完全明确**
- ✅ **实施步骤详细具体**
- ✅ **验收标准清晰可测**
- ✅ **风险控制机制完善**
- ✅ **专家支持承诺明确**

**现在立即开始M1的实施！** 🚀
