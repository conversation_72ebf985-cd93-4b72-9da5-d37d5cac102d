'use client';

import React, { useRef, useEffect, useCallback, useState } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';
import rehypeSanitize from 'rehype-sanitize';
import {
  splitContentWithStableIds,
  type ContentSegment,
  BoundaryIndex,
  calculateAffectedLineRange,
  findAffectedSegments,
  splitContentInRange
} from '@/lib/boundary-index';
import { markPerformance, PERF_EVENTS } from '@/components/debug/PerformanceMonitor';
import { getWorkerManager, isWorkerSupported } from '@/lib/worker-manager';

interface NativeDOMRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
  changes?: any; // CodeMirror changes for incremental parsing
}

// ContentSegment 接口现在从 boundary-index.ts 导入

// 旧的hashString函数已移除，现在使用 boundary-index.ts 中的 fastHash32

// 渲染单个段落 - 添加安全基线
const renderSegment = (content: string): string => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeSanitize) // 添加内容消毒
      .use(rehypeStringify);

    let result = processor.processSync(content);
    let htmlString = String(result);
    
    // 为标题添加ID
    htmlString = htmlString.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );

    // 添加安全基线处理
    htmlString = processExternalLinks(htmlString);
    htmlString = processImages(htmlString);

    return htmlString;
  } catch (error) {
    console.error('Segment rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

// 安全基线：处理外链
const processExternalLinks = (html: string): string => {
  return html.replace(
    /<a\s+href="https?:\/\/[^"]*"/g,
    '$& rel="noopener noreferrer"'
  );
};

// 安全基线：处理图片懒加载
const processImages = (html: string): string => {
  return html.replace(
    /<img\s+src="([^"]*)"([^>]*)>/g,
    '<img loading="lazy" src="$1" width="auto" height="auto"$2>'
  );
};

// 使用新的稳定ID分割函数
const splitContent = splitContentWithStableIds;

// Worker渲染函数
const renderSegmentsWithWorker = async (segments: ContentSegment[]): Promise<ContentSegment[]> => {
  try {
    const workerManager = await getWorkerManager();
    const results = await workerManager.parseSegments(segments);

    // 将Worker结果映射回ContentSegment格式
    const renderedSegments = segments.map(segment => {
      const result = results.find(r => r.id === segment.id);
      if (result) {
        return {
          ...segment,
          element: undefined // 重置element，让DOM更新逻辑重新创建
        };
      }
      return segment;
    });

    // 缓存渲染结果
    results.forEach(result => {
      const segment = renderedSegments.find(s => s.id === result.id);
      if (segment) {
        // 将HTML结果缓存到某个地方，供DOM更新使用
        (segment as any).cachedHtml = result.html;
        (segment as any).metadata = result.metadata;
      }
    });

    return renderedSegments;
  } catch (error) {
    console.error('Worker渲染失败，降级到主线程:', error);
    throw error;
  }
};

/**
 * keyed-diff DOM更新算法 - 支持add/remove/update/move操作
 * 避免innerHTML清空，实现真正的增量更新
 */
const updateDOMKeyed = (
  container: HTMLElement,
  oldSegments: ContentSegment[],
  newSegments: ContentSegment[]
) => {
  // 构建映射表
  const oldMap = new Map(oldSegments.map(seg => [seg.id, seg]));
  const newMap = new Map(newSegments.map(seg => [seg.id, seg]));

  // 获取现有DOM元素映射
  const existingElements = Array.from(container.children) as HTMLElement[];
  const elementMap = new Map<string, HTMLElement>();

  existingElements.forEach(el => {
    const segmentId = el.getAttribute('data-segment-id');
    if (segmentId) {
      elementMap.set(segmentId, el);
    }
  });

  // 计算操作列表
  interface DOMOperation {
    type: 'add' | 'remove' | 'update' | 'move';
    segmentId: string;
    element?: HTMLElement;
    newIndex?: number;
    segment?: ContentSegment;
  }

  const operations: DOMOperation[] = [];

  // 1. 标记需要删除的元素
  oldSegments.forEach(oldSeg => {
    if (!newMap.has(oldSeg.id)) {
      const element = elementMap.get(oldSeg.id);
      if (element) {
        operations.push({
          type: 'remove',
          segmentId: oldSeg.id,
          element
        });
      }
    }
  });

  // 2. 处理新增、更新和移动
  newSegments.forEach((newSeg, index) => {
    const oldSeg = oldMap.get(newSeg.id);
    const existingElement = elementMap.get(newSeg.id);

    if (!oldSeg) {
      // 新增段落
      operations.push({
        type: 'add',
        segmentId: newSeg.id,
        newIndex: index,
        segment: newSeg
      });
    } else if (oldSeg.hash !== newSeg.hash) {
      // 内容变化，需要更新
      operations.push({
        type: 'update',
        segmentId: newSeg.id,
        element: existingElement,
        segment: newSeg
      });
    }

    // 检查位置是否需要调整（移动操作）
    if (existingElement) {
      const currentIndex = Array.from(container.children).indexOf(existingElement);
      if (currentIndex !== index && currentIndex !== -1) {
        operations.push({
          type: 'move',
          segmentId: newSeg.id,
          element: existingElement,
          newIndex: index
        });
      }
    }
  });

  // 3. 执行DOM操作 - 按类型分组执行，避免索引混乱

  // 先执行删除操作
  operations.filter(op => op.type === 'remove').forEach(op => {
    if (op.element && op.element.parentNode === container) {
      container.removeChild(op.element);
    }
  });

  // 再执行更新操作
  operations.filter(op => op.type === 'update').forEach(op => {
    if (op.element && op.segment) {
      op.element.innerHTML = renderSegment(op.segment.content);
    }
  });

  // 最后执行添加和移动操作，使用DocumentFragment批量处理
  const fragment = document.createDocumentFragment();
  const elementsToPlace: Array<{ element: HTMLElement; index: number }> = [];

  // 收集所有需要放置的元素
  newSegments.forEach((segment, index) => {
    let element = elementMap.get(segment.id);

    if (!element) {
      // 创建新元素
      element = document.createElement('div');
      element.className = 'markdown-segment';
      element.setAttribute('data-segment-id', segment.id);

      // 使用缓存的HTML或实时渲染
      const cachedHtml = (segment as any).cachedHtml;
      element.innerHTML = cachedHtml || renderSegment(segment.content);
    }

    elementsToPlace.push({ element, index });
  });

  // 一次性重新排列所有元素
  elementsToPlace
    .sort((a, b) => a.index - b.index)
    .forEach(({ element }) => {
      fragment.appendChild(element);
    });

  // 一次性提交到DOM
  container.replaceChildren(fragment);
};

export default function NativeDOMRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange,
  changes
}: NativeDOMRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const segmentsRef = useRef<ContentSegment[]>([]);
  const scrollRestoredRef = useRef(false);
  const boundaryIndexRef = useRef<BoundaryIndex | null>(null);
  const lastContentRef = useRef<string>('');
  const [useWorker, setUseWorker] = useState(false);
  const [workerError, setWorkerError] = useState<string | null>(null);

  // 初始化Worker
  useEffect(() => {
    if (isWorkerSupported()) {
      getWorkerManager()
        .then(() => {
          setUseWorker(true);
          setWorkerError(null);
        })
        .catch((error) => {
          setUseWorker(false);
          setWorkerError(error.message);
        });
    } else {
      setUseWorker(false);
      setWorkerError('Web Worker not supported');
    }
  }, []);

  // keyed-diff 增量更新DOM - 禁止innerHTML清空
  const updateDOM = useCallback((newSegments: ContentSegment[]) => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const oldSegments = segmentsRef.current;

    // 使用keyed-diff算法进行增量更新
    updateDOMKeyed(container, oldSegments, newSegments);

    // 更新引用
    segmentsRef.current = newSegments;
  }, []);

  // 局部重分段逻辑（支持Worker）
  const updateSegmentsIncremental = useCallback(async (newContent: string, changes?: any) => {
    // 如果没有变更信息或内容完全不同，使用全量分段
    if (!changes || !boundaryIndexRef.current || lastContentRef.current !== content) {
      boundaryIndexRef.current = new BoundaryIndex(newContent);
      const newSegments = splitContent(newContent);

      // 尝试使用Worker解析
      if (useWorker && !workerError) {
        try {
          const renderedSegments = await renderSegmentsWithWorker(newSegments);
          lastContentRef.current = newContent;
          updateDOM(renderedSegments);
          return;
        } catch (error) {
          setWorkerError(error.message);
          setUseWorker(false);
        }
      }

      // 主线程解析
      lastContentRef.current = newContent;
      updateDOM(newSegments);
      return;
    }

    // 更新BoundaryIndex
    boundaryIndexRef.current.applyChanges(changes);

    // 计算受影响的范围
    const affectedLineRange = calculateAffectedLineRange(changes, boundaryIndexRef.current);

    // 找到受影响的段落
    const oldSegments = segmentsRef.current;
    const affectedSegmentRange = findAffectedSegments(oldSegments, affectedLineRange, boundaryIndexRef.current);

    // 重新分析受影响的区域
    const startOffset = boundaryIndexRef.current.getLineStart(affectedLineRange.start);
    const endOffset = boundaryIndexRef.current.getLineStart(affectedLineRange.end + 1);
    const newSegmentsInRange = splitContentInRange(newContent, startOffset, endOffset, boundaryIndexRef.current);

    // 尝试使用Worker解析受影响的段落
    if (useWorker && !workerError && newSegmentsInRange.length > 0) {
      try {
        const renderedSegmentsInRange = await renderSegmentsWithWorker(newSegmentsInRange);

        // 合并结果
        const newSegments = [
          ...oldSegments.slice(0, affectedSegmentRange.start),
          ...renderedSegmentsInRange,
          ...oldSegments.slice(affectedSegmentRange.end + 1)
        ];

        lastContentRef.current = newContent;
        updateDOM(newSegments);
        return;
      } catch (error) {
        setWorkerError(error.message);
        setUseWorker(false);
      }
    }

    // 主线程局部解析
    const newSegments = [
      ...oldSegments.slice(0, affectedSegmentRange.start),
      ...newSegmentsInRange,
      ...oldSegments.slice(affectedSegmentRange.end + 1)
    ];

    lastContentRef.current = newContent;
    updateDOM(newSegments);
  }, [content, updateDOM, useWorker, workerError]);

  // 监听内容变化
  useEffect(() => {
    // 性能标记：解析结束
    markPerformance(PERF_EVENTS.PARSE_END);

    updateSegmentsIncremental(content, changes);
  }, [content, changes, updateSegmentsIncremental]);

  // 初始滚动位置恢复
  useEffect(() => {
    if (containerRef.current && 
        initialScrollPosition !== undefined && 
        !scrollRestoredRef.current) {
      
      requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
          scrollRestoredRef.current = true;
        }
      });
    }
  }, [initialScrollPosition]);

  // 滚动事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  // 监听跳转事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
} 