/**
 * 测试Worker - 最简单版本
 */

// Worker script loaded

// 简单的Markdown解析函数
function parseMarkdown(content) {
  let html = content;
  
  // 基本的Markdown转换
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
  html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');
  html = html.replace(/`([^`]+)`/gim, '<code>$1</code>');
  
  // 处理段落
  html = html.replace(/\n\n/gim, '</p><p>');
  html = '<p>' + html + '</p>';
  html = html.replace(/<p><h/gim, '<h');
  html = html.replace(/<\/h([1-6])><\/p>/gim, '</h$1>');
  html = html.replace(/<p><\/p>/gim, '');
  
  return html;
}

// 处理消息
self.onmessage = function(e) {
  const { type, rev, segments } = e.data;
  
  if (type === 'parse') {
    try {
      const results = segments.map(segment => ({
        id: segment.id,
        html: parseMarkdown(segment.content),
        metadata: {
          hasHeadings: segment.content.includes('#'),
          hasCode: segment.content.includes('`'),
          length: segment.content.length
        }
      }));
      
      self.postMessage({
        type: 'result',
        rev: rev,
        results: results
      });
      
    } catch (error) {
      self.postMessage({
        type: 'error',
        rev: rev,
        error: error.message
      });
    }
  }
};

// 发送就绪消息
self.postMessage({ type: 'ready' });
