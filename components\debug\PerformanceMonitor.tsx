'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// 性能指标接口
interface PerformanceMetrics {
  fps: number;
  p95Delay: number;
  longTaskCount: number;
  maxLongTask: number;
  domNodeCount: number;
  memoryUsage: number[];
  lastUpdateTime: number;
}

// 固定事件名 - 按照专家要求
export const PERF_EVENTS = {
  EDIT_START: 'edit-start',
  PARSE_END: 'parse-end',
  PREVIEW_COMMIT: 'preview-commit'
} as const;

// 性能监控Hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fps: 60,
    p95Delay: 0,
    longTaskCount: 0,
    maxLongTask: 0,
    domNodeCount: 0,
    memoryUsage: [],
    lastUpdateTime: Date.now()
  });

  const longTasks = useRef<number[]>([]);
  const delayMeasurements = useRef<number[]>([]);
  const fpsFrames = useRef<number[]>([]);
  const lastFrameTime = useRef<number>(performance.now());

  useEffect(() => {
    // 监控长任务
    if ('PerformanceObserver' in window) {
      const longTaskObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'longtask') {
            const duration = entry.duration;
            longTasks.current.push(duration);
            
            // 保持最近100个长任务记录
            if (longTasks.current.length > 100) {
              longTasks.current.shift();
            }
          }
        }
      });

      try {
        longTaskObserver.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.warn('Long task monitoring not supported');
      }

      return () => longTaskObserver.disconnect();
    }
  }, []);

  useEffect(() => {
    // 监控自定义性能标记
    if ('PerformanceObserver' in window) {
      const measureObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'measure') {
            if (entry.name === 'edit-to-preview') {
              delayMeasurements.current.push(entry.duration);
              
              // 保持最近100个测量记录
              if (delayMeasurements.current.length > 100) {
                delayMeasurements.current.shift();
              }
            }
          }
        }
      });

      try {
        measureObserver.observe({ entryTypes: ['measure'] });
      } catch (e) {
        console.warn('Performance measure monitoring not supported');
      }

      return () => measureObserver.disconnect();
    }
  }, []);

  useEffect(() => {
    // FPS监控
    let animationId: number;
    
    const measureFPS = () => {
      const now = performance.now();
      const delta = now - lastFrameTime.current;
      
      if (delta > 0) {
        const fps = 1000 / delta;
        fpsFrames.current.push(fps);
        
        // 保持最近60帧记录
        if (fpsFrames.current.length > 60) {
          fpsFrames.current.shift();
        }
      }
      
      lastFrameTime.current = now;
      animationId = requestAnimationFrame(measureFPS);
    };

    animationId = requestAnimationFrame(measureFPS);
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  useEffect(() => {
    // 定期更新指标
    const updateInterval = setInterval(() => {
      // 计算P95延迟
      const sortedDelays = [...delayMeasurements.current].sort((a, b) => a - b);
      const p95Index = Math.floor(sortedDelays.length * 0.95);
      const p95Delay = sortedDelays[p95Index] || 0;

      // 计算平均FPS
      const avgFPS = fpsFrames.current.length > 0 
        ? fpsFrames.current.reduce((sum, fps) => sum + fps, 0) / fpsFrames.current.length
        : 60;

      // 计算长任务统计
      const longTaskCount = longTasks.current.length;
      const maxLongTask = longTasks.current.length > 0 
        ? Math.max(...longTasks.current)
        : 0;

      // 计算DOM节点数
      const domNodeCount = document.querySelectorAll('*').length;

      // 内存使用情况（如果支持）
      let memoryUsage: number[] = [];
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        memoryUsage = [
          memory.usedJSHeapSize / 1024 / 1024, // MB
          memory.totalJSHeapSize / 1024 / 1024,
          memory.jsHeapSizeLimit / 1024 / 1024
        ];
      }

      setMetrics({
        fps: Math.round(avgFPS),
        p95Delay: Math.round(p95Delay),
        longTaskCount,
        maxLongTask: Math.round(maxLongTask),
        domNodeCount,
        memoryUsage,
        lastUpdateTime: Date.now()
      });
    }, 1000); // 每秒更新一次

    return () => clearInterval(updateInterval);
  }, []);

  return metrics;
};

// 性能标记工具函数
export const markPerformance = (eventName: string) => {
  if ('performance' in window && 'mark' in performance) {
    performance.mark(eventName);
  }
};

export const measurePerformance = (measureName: string, startMark: string, endMark: string) => {
  if ('performance' in window && 'measure' in performance) {
    try {
      performance.measure(measureName, startMark, endMark);
    } catch (e) {
      console.warn(`Failed to measure ${measureName}:`, e);
    }
  }
};

// 性能监控面板组件
interface PerformanceMonitorProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  isVisible,
  onToggle
}) => {
  const metrics = usePerformanceMonitor();

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-sm z-50"
      >
        性能监控
      </button>
    );
  }

  // 判断指标是否达标
  const getStatusColor = (value: number, threshold: number, isReverse = false) => {
    const isGood = isReverse ? value <= threshold : value >= threshold;
    return isGood ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="fixed bottom-4 right-4 w-80 z-50">
      <Card>
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="text-sm">性能监控</CardTitle>
            <button
              onClick={onToggle}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </CardHeader>
        <CardContent className="space-y-2 text-xs">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="text-gray-600">滚动FPS:</span>
              <span className={`ml-1 font-mono ${getStatusColor(metrics.fps, 55)}`}>
                {metrics.fps}
              </span>
            </div>
            <div>
              <span className="text-gray-600">输入延迟:</span>
              <span className={`ml-1 font-mono ${getStatusColor(metrics.p95Delay, 150, true)}`}>
                {metrics.p95Delay}ms
              </span>
            </div>
            <div>
              <span className="text-gray-600">长任务:</span>
              <span className={`ml-1 font-mono ${getStatusColor(metrics.maxLongTask, 50, true)}`}>
                {metrics.longTaskCount} ({metrics.maxLongTask}ms)
              </span>
            </div>
            <div>
              <span className="text-gray-600">DOM节点:</span>
              <span className={`ml-1 font-mono ${getStatusColor(metrics.domNodeCount, 2500, true)}`}>
                {metrics.domNodeCount}
              </span>
            </div>
          </div>
          
          {metrics.memoryUsage.length > 0 && (
            <div>
              <span className="text-gray-600">内存使用:</span>
              <div className="font-mono text-xs">
                {metrics.memoryUsage[0].toFixed(1)}MB / {metrics.memoryUsage[1].toFixed(1)}MB
              </div>
            </div>
          )}
          
          <div className="text-xs text-gray-500 pt-2 border-t">
            更新时间: {new Date(metrics.lastUpdateTime).toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
