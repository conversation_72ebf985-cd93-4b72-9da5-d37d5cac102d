# 在线编辑器长文性能改造（最终版·可直接执行）

> 目标（硬指标）：滚动 ≥ 55 FPS；输入→预览 P95 < 150 ms；单次长任务 < 50 ms；预览区 DOM 子节点 ≤ 2500；L 档长文连续滚动 2 分钟内存不持续上升 ≥ 10%。

---

## Milestone 规划

### M1（T+2 天）
- 稳定段 ID：`id = fastHash32(plainText) + ":" + startOffset`（哈希算法固定为 MurmurHash3 x86_32 或 FNV-1a 32，二选一）。
- 输入端合并窗口：rAF + 120 ms 防抖（先固定值，M2 做自适应）。
- 增量 DOM：基于 `data-segment-id` 的 keyed-diff（支持 add/remove/update/move），彻底禁止 `innerHTML=''` 全量替换。
- 滚动恢复：移除强制 `scrollTop`，暂用“最近 heading 锚点 + 段内相对偏移”的策略雏形。
- 性能埋点：接入 LongTasks 观测与 `performance.mark/measure`，在调试面板显示关键指标。
- 安全基线：解析结果进入 DOM 之前完成消毒（如 rehype-sanitize）；外链加 `rel="noopener noreferrer"`；图片懒加载并给出尺寸。

### M2（T+1 周）
- 局部重分段：利用编辑器 `update.changes` 只处理受影响窗口（标题 / 正文 / 代码块三类边界先覆盖）。
- 语义锚点滚动：优先“最近 heading + 段内相对位移”，像素仅兜底；图片懒加载后仍能回到同一语义位置。
- 自适应防抖：`debounce = clamp(80 + 0.004 * totalLines, 80, 200)`，输入静止 ≥ 300 ms 立即 trailing 刷新；大块变更直达增量管线。
- TOC 一次产出：解析阶段直接生成目录与稳定 heading-id，前后复用。

### M3（T+1–2 周）
- 解析与高亮迁入 Web Worker：纯函数插件链（remark-parse/gfm、remark-rehype、rehype-stringify）；仅对“已标注语言”高亮；未标注当纯文本。
- Worker 协议与幂等：主线程发 `rev`（递增版本号），回包带 `rev` 与段 `id`；只接收 `rev` 最新结果，丢弃陈旧回包。
- 视口内再高亮（大代码块）：首次仅输出未高亮 HTML，进入视口时再请求高亮结果替换。
- 数据驱动虚拟滚动：当“段落数 > 3000”或“DOM 子节点 > 2500 且滚动 FPS < 50”时启用（视口 ± 2 屏 overscan）。

---

## Blocking 事项（M1 前必须明确 / 完成）

1) 哈希算法与一致性：团队确定使用 MurmurHash3 x86_32 或 FNV-1a 32，并在段对象中缓存 `hash`，避免重复计算。  
2) keyed-diff 覆盖面：实现 move 与批量插入/删除的 O(n) 单趟算法，提交一次性 `replaceChildren(frag)`，避免多次布局抖动。  
3) 防抖旁路：定义“大块变更”阈值（建议：行数 ≥ 200 或 字节 ≥ 64 KB），此类操作绕过防抖直接走增量管线；`blur/保存` 立即刷新。  
4) 调试面板：固定事件名（如 `edit-start`、`parse-end`、`preview-commit`），展示 FPS、P95 延迟、长任务次数/最大值、DOM 子节点数、内存曲线。  
5) 安全与渲染纯度：确认消毒策略、外链与图片策略已在 M1 落地。

---

## 实施要点（简明版）

- startOffset 计算：维护 `lineStart` 前缀表；用 `update.changes` 增量更新；`toOffset({line,col}) = lineStart[line] + col`。  
- 变更映射：把每个变更区间映射到“受影响段索引区间”，外扩 1 段缓冲以覆盖跨边界合并/拆分。  
- DOM 更新：用 `data-segment-id` 建立映射；单趟按新顺序构建 fragment，复用节点即完成 move；一次提交。  
- Worker 通信：仅传回“段级 HTML + 元数据（如 TOC）”，不回传庞大 AST；失败自动降级到主线程解析+加强防抖。  
- 低成本提速（可与 M1 并行）：容器样式使用 `content-visibility: auto; contain: content;`。

---

## 基准与回归

- 测试文档：S（2–3k 行）/ M（8–10k 行）/ L（18–25k 行，含大 JSON/表格/列表）。  
- 验收门槛：滚动 ≥ 55 FPS；输入→预览 P95 < 150 ms；单次长任务 < 50 ms；DOM ≤ 2500；L 档 2 分钟滚动内存不持续上升 ≥ 10%。  
- 自动化用例：解析快照比对；滚动 2000px 可视段首 ID 变化 ≤ 1；大纲跳转在图片懒加载完成后仍定位正确；记录 5 s 窗口长任务统计。  
- 特性开关：`feature.worker`、`feature.incrementalSegments`、`feature.keyedDiff`、`feature.adaptiveDebounce`、`feature.virtualList`。  
- 降级路径：Worker 异常 → 主线程解析 + 提高防抖；虚拟滚动异常 → 渐进挂载（分批 append）。

---

## 我需要技术同学产出的材料（用于复核）

- M1 完成后的指标截图/数据：S 与 M 档的 FPS、P95 延迟、长任务分布、DOM 子节点、内存曲线。  
- 关键代码片段：稳定段 ID 生成；keyed-diff 的 move/批量路径；`update.changes` → 受影响段映射；消毒与图片/外链策略。  
- Worker 协议草案：请求/回包字段与错误处理（含 `rev` 策略）。

---

## 备注

如在 `applyChanges`（多变更片段合并）、keyed-diff 移动路径、或 Worker 打包与插件选择上遇到阻碍，我可以提供最小可运行示例（含单测）与一份可直接集成的 Worker 解析骨架，保证 M1–M2 节点按期达成。
