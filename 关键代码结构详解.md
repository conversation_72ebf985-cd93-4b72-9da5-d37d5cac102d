# Markdown编辑器关键代码结构详解

## 1. 编辑内核架构

### 1.1 CodeMirror 6 编辑器核心

#### 核心组件：`components/editor/CodeMirrorEditor.tsx`

```typescript
// 编辑器状态创建
const state = EditorState.create({
  doc: value,
  extensions: [
    basicSetup,                    // 基础编辑功能
    markdown(),                    // Markdown 语法高亮
    theme === 'dark' ? oneDark : [], // 主题切换
    EditorView.updateListener.of((update) => {
      if (update.docChanged) {
        const newValue = update.state.doc.toString();
        onChange(newValue);          // 内容变化回调
      }
      if (update.viewportChanged && onScrollPositionChange) {
        const scrollTop = update.view.scrollDOM.scrollTop;
        onScrollPositionChange(scrollTop); // 滚动位置同步
      }
    }),
    // 自定义样式主题
    EditorView.theme({
      '&': {
        height: '100%',
        fontSize: '14px',
        fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace'
      },
      '.cm-content': {
        padding: '16px',
        lineHeight: '1.6',
        minHeight: '100%'
      },
      '.cm-focused': { outline: 'none' },
      '.cm-editor': { height: '100%' },
      '.cm-scroller': { fontFamily: 'inherit' }
    }),
    EditorView.lineWrapping           // 自动换行
  ]
});
```

#### 关键特性实现

1. **实时内容同步**
   - 通过 `updateListener` 监听文档变化
   - 防抖处理避免频繁更新
   - 自动保存到本地存储和 Git 服务

2. **滚动位置管理**
   - 监听 `viewportChanged` 事件
   - 精确记录和恢复滚动位置
   - 支持编辑器与预览区域同步滚动

3. **主题适配**
   - 动态切换明暗主题
   - 自定义语法高亮配色
   - 响应系统主题变化

### 1.2 编辑器模式管理

#### 三种编辑模式：`components/editor/EditorArea.tsx`

```typescript
// 模式切换逻辑
{editorMode === 'source' && (
  <div className="flex-1 editor-content w-full">
    <CodeMirrorEditor
      value={content}
      onChange={handleContentChange}
      placeholder="开始编写您的 Markdown..."
      className="h-full w-full"
      initialScrollPosition={initialScrollPosition?.editor}
      onScrollPositionChange={(pos) => onScrollPositionChange?.(pos, undefined)}
    />
  </div>
)}

{editorMode === 'preview' && (
  <div className="flex-1 editor-content w-full">
    <NativeDOMRenderer
      content={content}
      initialScrollPosition={initialScrollPosition?.preview}
      onScrollPositionChange={(pos: number) => onScrollPositionChange?.(undefined, pos)}
    />
  </div>
)}

{editorMode === 'split' && (
  <>
    <div className="flex-1 border-r border-border editor-content w-1/2">
      <CodeMirrorEditor /* 编辑器配置 */ />
    </div>
    <div className="flex-1 editor-content w-1/2">
      <NativeDOMRenderer /* 预览配置 */ />
    </div>
  </>
)}
```

## 2. 预览刷新机制

### 2.1 高性能增量渲染器

#### 核心组件：`components/editor/NativeDOMRenderer.tsx`

```typescript
// 内容分段结构
interface ContentSegment {
  id: string;
  content: string;
  hash: string;        // 内容哈希，用于检测变化
  element?: HTMLElement;
}

// 智能内容分割
const splitContent = (content: string): ContentSegment[] => {
  const lines = content.split('\n');
  const segments: ContentSegment[] = [];
  let currentSegment = '';
  let segmentIndex = 0;
  let inCodeBlock = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测代码块边界
    if (line.trim().startsWith('```')) {
      inCodeBlock = !inCodeBlock;
      currentSegment += (currentSegment ? '\n' : '') + line;
      
      if (!inCodeBlock && currentSegment.trim()) {
        segments.push({
          id: `segment-${segmentIndex}`,
          content: currentSegment.trim(),
          hash: hashContent(currentSegment.trim())
        });
        segmentIndex++;
        currentSegment = '';
      }
      continue;
    }

    // 段落分割逻辑
    if (!inCodeBlock && line.trim() === '') {
      if (currentSegment.trim()) {
        segments.push({
          id: `segment-${segmentIndex}`,
          content: currentSegment.trim(),
          hash: hashContent(currentSegment.trim())
        });
        segmentIndex++;
        currentSegment = '';
      }
    } else {
      currentSegment += (currentSegment ? '\n' : '') + line;
    }
  }

  return segments;
};
```

#### 精确增量更新

```typescript
// 精确增量更新DOM
const updateDOM = useCallback((newSegments: ContentSegment[]) => {
  if (!containerRef.current) return;

  const container = containerRef.current;
  const oldSegments = segmentsRef.current;
  
  // 保存当前滚动位置
  const currentScrollTop = container.scrollTop;
  
  // 创建新段落映射
  const newSegmentMap = new Map(newSegments.map(seg => [seg.id, seg]));
  const oldSegmentMap = new Map(oldSegments.map(seg => [seg.id, seg]));
  
  // 获取现有DOM元素
  const existingElements = Array.from(container.children) as HTMLElement[];
  const elementMap = new Map<string, HTMLElement>();
  
  existingElements.forEach(el => {
    const segmentId = el.getAttribute('data-segment-id');
    if (segmentId) {
      elementMap.set(segmentId, el);
    }
  });

  // 创建新的DOM结构
  const fragment = document.createDocumentFragment();
  
  newSegments.forEach((segment, index) => {
    const oldSegment = oldSegmentMap.get(segment.id);
    const existingElement = elementMap.get(segment.id);
    
    // 如果内容没有变化且DOM元素存在，直接复用
    if (oldSegment && 
        existingElement && 
        oldSegment.hash === segment.hash) {
      fragment.appendChild(existingElement);
      segment.element = existingElement;
    } else {
      // 创建新元素
      const div = document.createElement('div');
      div.className = 'markdown-segment';
      div.setAttribute('data-segment-id', segment.id);
      div.innerHTML = renderSegment(segment.content);
      fragment.appendChild(div);
      segment.element = div;
    }
  });

  // 一次性替换所有内容
  container.innerHTML = '';
  container.appendChild(fragment);
  
  // 精确恢复滚动位置
  container.scrollTop = currentScrollTop;
  
  // 更新引用
  segmentsRef.current = newSegments;
}, []);
```

### 2.2 Unified.js 处理管道

#### Markdown 解析与转换

```typescript
// 统一的渲染管道
const renderSegment = (content: string): string => {
  try {
    const processor = unified()
      .use(remarkParse)                    // Markdown 解析
      .use(remarkGfm)                      // GitHub Flavored Markdown
      .use(remarkRehype, { 
        allowDangerousHtml: true           // 允许原始 HTML
      })
      .use(rehypeRaw)                      // 处理原始 HTML
      .use(rehypeHighlight)                // 代码语法高亮
      .use(rehypeStringify);               // 转换为 HTML 字符串

    let result = processor.processSync(content);
    let htmlString = String(result);
    
    // 为标题添加锚点ID
    htmlString = htmlString.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')  // 保留中英文字符
          .replace(/\s+/g, '-')                    // 空格转连字符
          .replace(/-+/g, '-')                     // 多个连字符合并
          .replace(/^-|-$/g, '');                  // 移除首尾连字符
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );
    
    return htmlString;
  } catch (error) {
    console.error('Segment rendering error:', error);
    return '<p>渲染错误</p>';
  }
};
```

## 3. 图片处理机制

### 3.1 图片路径处理

#### Next.js 图片优化配置

```javascript
// next.config.js
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { 
    unoptimized: true    // 禁用图片优化，支持外部图片
  },
};
```

#### 头像图片处理

```typescript
// 用户头像组件处理
<Avatar className="w-6 h-6">
  <AvatarImage src={session.user.githubAvatar || session.user.image || ''} />
  <AvatarFallback>
    <User className="w-3 h-3" />
  </AvatarFallback>
</Avatar>

// Git 提交历史中的头像
{commit.author?.avatar_url ? (
  <img
    src={commit.author.avatar_url}
    alt={commit.author.login}
    className="w-3 h-3 rounded-full flex-shrink-0"
  />
) : (
  <User className="w-3 h-3 flex-shrink-0" />
)}
```

### 3.2 Markdown 图片渲染

#### 通过 Unified.js 处理

```typescript
// 图片在 Markdown 中的处理流程
// 1. remark-parse 解析 ![alt](src) 语法
// 2. remark-rehype 转换为 HTML <img> 标签
// 3. rehype-stringify 输出最终 HTML

// 示例：![图片描述](./images/example.png)
// 转换为：<img src="./images/example.png" alt="图片描述" />
```

#### 图片懒加载和错误处理

```typescript
// 在渲染后的 HTML 中，可以通过 CSS 和 JavaScript 增强
// 1. 添加懒加载属性
// 2. 错误处理回退
// 3. 响应式图片支持

// CSS 样式增强
const imageStyles = `
  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 1em 0;
  }
  
  img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s;
  }
  
  img[loading="lazy"].loaded {
    opacity: 1;
  }
`;
```

## 4. 数学公式处理

### 4.1 当前状态分析

根据代码分析，当前项目**暂未实现**专门的数学公式渲染功能。项目使用的是标准的 Unified.js 管道，没有集成 KaTeX 或 MathJax。

### 4.2 数学公式扩展方案

#### 方案一：集成 KaTeX

```typescript
// 需要添加的依赖
// npm install katex rehype-katex remark-math

import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';

// 扩展渲染管道
const processor = unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkMath)                    // 解析数学公式语法
  .use(remarkRehype, { 
    allowDangerousHtml: true 
  })
  .use(rehypeRaw)
  .use(rehypeKatex)                   // 渲染 KaTeX
  .use(rehypeHighlight)
  .use(rehypeStringify);

// CSS 样式引入
import 'katex/dist/katex.min.css';
```

#### 方案二：集成 MathJax

```typescript
// 需要添加的依赖
// npm install mathjax-full rehype-mathjax remark-math

import remarkMath from 'remark-math';
import rehypeMathjax from 'rehype-mathjax';

// 扩展渲染管道
const processor = unified()
  .use(remarkParse)
  .use(remarkGfm)
  .use(remarkMath)                    // 解析数学公式语法
  .use(remarkRehype, { 
    allowDangerousHtml: true 
  })
  .use(rehypeRaw)
  .use(rehypeMathjax)                 // 渲染 MathJax
  .use(rehypeHighlight)
  .use(rehypeStringify);
```

### 4.3 公式语法支持

```markdown
<!-- 行内公式 -->
这是一个行内公式：$E = mc^2$

<!-- 块级公式 -->
$$
\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}
$$

<!-- 复杂公式示例 -->
$$
\begin{align}
\nabla \times \vec{\mathbf{B}} -\, \frac1c\, \frac{\partial\vec{\mathbf{E}}}{\partial t} &= \frac{4\pi}{c}\vec{\mathbf{j}} \\
\nabla \cdot \vec{\mathbf{E}} &= 4 \pi \rho \\
\nabla \times \vec{\mathbf{E}}\, +\, \frac1c\, \frac{\partial\vec{\mathbf{B}}}{\partial t} &= \vec{\mathbf{0}} \\
\nabla \cdot \vec{\mathbf{B}} &= 0
\end{align}
$$
```

## 5. 性能优化策略

### 5.1 渲染性能优化

1. **内容哈希比较** - 只重新渲染变化的段落
2. **DOM 元素复用** - 复用未变化的 DOM 节点
3. **批量更新** - 使用 DocumentFragment 减少重排
4. **防抖处理** - 避免频繁的重新渲染
5. **虚拟滚动** - 大文档的高效渲染（待实现）

### 5.2 内存管理

1. **及时清理** - 组件卸载时清理事件监听器
2. **缓存策略** - 智能的渲染结果缓存
3. **懒加载** - 按需加载组件和资源

## 6. 状态管理与数据流

### 6.1 应用状态结构

#### 核心状态接口：`lib/storage.ts`

```typescript
// 应用状态接口
export interface AppState {
  currentFile: string | null;           // 当前打开的文件
  isLeftSidebarCollapsed: boolean;      // 左侧边栏折叠状态
  isRightSidebarOpen: boolean;          // 右侧边栏打开状态
  isViewingCommit: boolean;             // 是否在查看提交详情
  content: string;                      // 当前文件内容
  scrollPosition?: {                    // 滚动位置记录
    editor?: number;
    preview?: number;
  };
  leftSidebarActiveTab?: string;        // 左侧边栏活跃标签页
}
```

#### 状态管理 Hook

```typescript
// 自定义状态管理 Hook
export const useAppState = (initialState: Partial<AppState>) => {
  const [state, setState] = useState<AppState>({
    currentFile: null,
    isLeftSidebarCollapsed: false,
    isRightSidebarOpen: false,
    isViewingCommit: false,
    content: '',
    ...initialState
  });

  // 更新状态的方法
  const updateState = useCallback((updates: Partial<AppState>) => {
    setState(prev => {
      const newState = { ...prev, ...updates };
      // 自动保存到本地存储
      LocalStorage.saveAppState(newState);
      return newState;
    });
  }, []);

  // 保存滚动位置
  const saveScrollPosition = useCallback((editor?: number, preview?: number) => {
    updateState({
      scrollPosition: {
        editor: editor ?? state.scrollPosition?.editor,
        preview: preview ?? state.scrollPosition?.preview
      }
    });
  }, [state.scrollPosition, updateState]);

  return { state, updateState, saveScrollPosition };
};
```

### 6.2 文件系统抽象层

#### Git 服务封装：`lib/simpleGit.ts`

```typescript
// Git 服务单例类
export class SimpleGitService {
  private static instance: SimpleGitService;
  private files: Map<string, string> = new Map();           // 文件内容映射
  private baselineFiles: Map<string, string> = new Map();   // 基线文件（用于对比）
  private stagedFiles: Set<string> = new Set();             // 暂存文件集合

  // 文件操作方法
  createFile(filepath: string, content: string = ''): void {
    this.files.set(filepath, content);
    this.saveToStorage();
    console.log(`文件已创建: ${filepath}`);
  }

  updateFile(filepath: string, content: string): void {
    this.files.set(filepath, content);
    this.saveToStorage();
    console.log(`文件已更新: ${filepath}, 内容长度: ${content.length}`);
  }

  getFileContent(filepath: string): string | null {
    return this.files.get(filepath) || null;
  }

  deleteFile(filepath: string): void {
    this.files.delete(filepath);
    this.stagedFiles.delete(filepath);
    this.baselineFiles.delete(filepath);
    this.saveToStorage();
    console.log(`文件已删除: ${filepath}`);
  }

  renameFile(oldPath: string, newPath: string): void {
    const content = this.files.get(oldPath);
    if (!content) {
      throw new Error(`文件不存在: ${oldPath}`);
    }

    this.files.set(newPath, content);
    this.files.delete(oldPath);

    // 更新暂存状态
    if (this.stagedFiles.has(oldPath)) {
      this.stagedFiles.delete(oldPath);
      this.stagedFiles.add(newPath);
    }

    this.saveToStorage();
    console.log(`文件重命名成功: ${oldPath} -> ${newPath}`);
  }

  // Git 操作方法
  stageFile(filepath: string): void {
    if (this.files.has(filepath)) {
      const currentContent = this.files.get(filepath);
      const baselineContent = this.baselineFiles.get(filepath);

      if (currentContent !== baselineContent) {
        this.stagedFiles.add(filepath);
        this.saveToStorage();
        console.log(`文件已暂存: ${filepath}`);
      }
    }
  }

  unstageFile(filepath: string): void {
    this.stagedFiles.delete(filepath);
    this.saveToStorage();
    console.log(`文件已取消暂存: ${filepath}`);
  }

  commit(message: string): string {
    if (this.stagedFiles.size === 0) {
      throw new Error('没有暂存的文件可以提交');
    }

    const commitId = `commit-${Date.now()}`;

    // 将暂存的文件内容复制到基线
    this.stagedFiles.forEach(filepath => {
      const content = this.files.get(filepath);
      if (content !== undefined) {
        this.baselineFiles.set(filepath, content);
      }
    });

    // 清空暂存区
    this.stagedFiles.clear();
    this.saveToStorage();

    console.log(`提交成功: ${commitId}, 消息: ${message}`);
    return commitId;
  }
}
```

### 6.3 数据持久化策略

#### 本地存储管理

```typescript
// 本地存储工具类
export class LocalStorage {
  private static readonly CONTENT_KEY = 'markdown-editor-content';
  private static readonly SETTINGS_KEY = 'markdown-editor-settings';
  private static readonly APP_STATE_KEY = 'markdown-editor-app-state';

  // 保存应用状态
  static saveAppState(state: AppState): void {
    try {
      const stateToSave = {
        ...state,
        // 不保存敏感或临时数据
        content: undefined  // 内容通过 Git 服务管理
      };
      localStorage.setItem(this.APP_STATE_KEY, JSON.stringify(stateToSave));
    } catch (error) {
      console.error('Failed to save app state:', error);
    }
  }

  // 加载应用状态
  static loadAppState(): Partial<AppState> {
    try {
      const saved = localStorage.getItem(this.APP_STATE_KEY);
      return saved ? JSON.parse(saved) : {};
    } catch (error) {
      console.error('Failed to load app state:', error);
      return {};
    }
  }

  // 自动保存功能
  static enableAutoSave(callback: () => void, interval: number = 30000): () => void {
    const intervalId = setInterval(callback, interval);

    // 页面卸载时保存
    const handleBeforeUnload = () => {
      callback();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 返回清理函数
    return () => {
      clearInterval(intervalId);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }
}
```

### 6.4 实时同步机制

#### 内容变化处理流程

```typescript
// 主应用中的内容变化处理
onContentChange={async (newContent, editorCurrentFile) => {
  console.log(`=== 编辑器内容变化 ===`);
  console.log(`- 新内容长度: ${newContent.length}`);

  // 1. 更新应用状态
  updateState({ content: newContent });
  setIsSaved(false);

  // 2. 确定目标文件
  const targetFile = editorCurrentFile || currentFileRef.current;

  // 3. 自动同步到Git服务
  if (targetFile) {
    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();

      // 检查同步前后状态
      const beforeContent = gitService.getFileContent(targetFile);
      console.log(`- 同步前Git中的内容长度: ${beforeContent?.length || 0}`);

      gitService.updateFile(targetFile, newContent);

      const afterContent = gitService.getFileContent(targetFile);
      console.log(`- 同步后Git中的内容长度: ${afterContent?.length || 0}`);

      console.log(`✅ 内容已同步到Git服务: ${targetFile}`);

      // 4. 刷新Git状态
      refreshGitStatus();
    } catch (error) {
      console.error('❌ 同步到Git服务失败:', error);
    }
  }
}}
```

## 7. 组件通信架构

### 7.1 事件驱动通信

#### 自定义事件系统

```typescript
// 大纲跳转事件
const handleOutlineItemClick = (headingId: string, lineNumber: number) => {
  if (editorMode === 'source' || editorMode === 'split') {
    // 跳转到编辑器指定行
    const event = new CustomEvent('scrollToEditorLine', {
      detail: { lineNumber }
    });
    document.dispatchEvent(event);
  }

  if (editorMode === 'preview' || editorMode === 'split') {
    // 跳转到预览区域指定标题
    const event = new CustomEvent('scrollToPreviewHeading', {
      detail: { headingId }
    });
    document.dispatchEvent(event);
  }
};

// 监听跳转事件
useEffect(() => {
  const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
    if (containerRef.current) {
      const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
      if (headingElement) {
        headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  };

  document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);

  return () => {
    document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
  };
}, []);
```

### 7.2 Props 传递链

#### 父子组件通信模式

```typescript
// 主应用 -> 编辑器区域
<EditorArea
  onSettingsToggle={() => updateState({ isRightSidebarOpen: !isRightSidebarOpen })}
  content={content}
  currentFile={currentFile}
  initialScrollPosition={state.scrollPosition}
  onScrollPositionChange={saveScrollPosition}
  onContentChange={handleContentChange}
/>

// 主应用 -> 左侧边栏
<LeftSidebar
  isCollapsed={isLeftSidebarCollapsed}
  onToggle={() => updateState({ isLeftSidebarCollapsed: !isLeftSidebarCollapsed })}
  onFileSelect={handleFileSelect}
  currentFile={currentFile}
  currentFileContent={content}
  gitStatusVersion={gitStatusVersion}
  onCommitContentChange={handleCommitContentChange}
  activeTab={leftSidebarActiveTab || 'outline'}
  onActiveTabChange={(tab) => updateState({ leftSidebarActiveTab: tab })}
/>
```

这个架构展现了现代前端编辑器的设计精髓，通过精心设计的增量更新机制、模块化架构和完善的状态管理，实现了高性能的实时编辑体验。
