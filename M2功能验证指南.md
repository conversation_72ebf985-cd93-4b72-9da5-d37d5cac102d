# M2 功能验证指南

## 🎯 验证目标

验证M2阶段实现的两个核心功能：
1. **局部重分段** - 增量处理，减少长任务
2. **Worker解析** - 主线程解放，并行处理

## 📋 验证步骤

### 1. 基础功能验证

#### 1.1 打开应用
- 访问 http://localhost:3001
- 确保应用正常加载
- 检查性能监控面板是否显示

#### 1.2 检查Worker状态
- 打开浏览器开发者工具 (F12)
- 查看Console标签页
- 应该看到以下日志之一：
  ```
  ✅ Worker初始化成功
  或
  ⚠️ Worker初始化失败，降级到主线程解析
  ```

### 2. 局部重分段验证

#### 2.1 创建测试文档
在编辑器中输入以下内容：
```markdown
# 标题1

这是第一段内容。

## 标题2

这是第二段内容。

```javascript
function test() {
  console.log("代码块");
}
```

这是第三段内容。

### 标题3

最后一段内容。
```

#### 2.2 测试局部编辑
1. **在文档开头插入内容**：
   - 在第一行前添加新行："# 新标题"
   - 观察Console，应该看到：
     ```
     ⚡ 局部重分段开始
     📍 受影响行范围: X - Y
     📍 受影响段落范围: X - Y
     ✅ 局部重分段完成: N -> M 段落
     ```

2. **在文档中间编辑**：
   - 修改"这是第二段内容"为"这是修改后的第二段"
   - 应该看到局部重分段日志，而不是全量重分段

3. **在文档末尾添加内容**：
   - 添加新段落："## 新增段落\n\n新增的内容。"
   - 验证只有受影响的部分被重新处理

### 3. Worker解析验证

#### 3.1 检查Worker工作状态
- 在Console中查找以下日志：
  ```
  🚀 使用Worker进行全量解析
  或
  🚀 使用Worker解析N个受影响段落
  ```

#### 3.2 测试Worker降级
如果Worker不可用，应该看到：
```
🔧 使用主线程解析
```

#### 3.3 大文档测试
创建一个较大的文档（复制粘贴多次上述内容），观察：
- Worker是否正常处理大量段落
- 是否有超时或错误
- 性能监控面板的长任务时间变化

### 4. 性能对比验证

#### 4.1 M1 vs M2 对比
使用性能监控面板观察以下指标：

| 指标 | M1基线 | M2目标 | 验证方法 |
|------|--------|--------|----------|
| **长任务** | 1145ms | <50ms | 连续编辑大文档 |
| **输入延迟** | 3ms | ≤3ms | 快速连续输入 |
| **滚动FPS** | 130 | ≥130 | 保持流畅滚动 |

#### 4.2 具体测试场景
1. **大段粘贴测试**：
   - 复制大量文本（>200行）
   - 一次性粘贴到编辑器
   - 观察长任务时间是否<50ms

2. **连续编辑测试**：
   - 快速连续修改不同段落
   - 观察是否使用局部重分段
   - 检查响应性是否保持良好

## 🔍 预期结果

### ✅ 成功指标

#### Console日志应该显示：
```
✅ Worker初始化成功
⚡ 局部重分段开始
📍 受影响行范围: 0 - 2
📍 受影响段落范围: 0 - 1
🚀 使用Worker解析2个受影响段落
✅ Worker局部重分段完成: 5 -> 6 段落
```

#### 性能监控面板应该显示：
- **滚动FPS**: ≥55 (绿色)
- **输入延迟**: <150ms (绿色)
- **长任务**: <50ms (绿色)
- **DOM节点**: 实时计数

### ⚠️ 降级场景

如果Worker不可用，应该看到：
```
⚠️ Worker初始化失败，降级到主线程解析
🔧 使用主线程解析
✅ 主线程局部重分段完成: 5 -> 6 段落
```

## 🐛 常见问题排查

### 1. Worker加载失败
**症状**：Console显示Worker初始化失败
**原因**：Worker文件路径或CORS问题
**解决**：检查 `/workers/markdown-parser.js` 是否可访问

### 2. 局部重分段不工作
**症状**：总是显示"全量重分段"
**原因**：changes信息传递链断裂
**排查**：检查CodeMirror → EditorArea → NativeDOMRenderer的参数传递

### 3. 性能没有提升
**症状**：长任务时间仍然很高
**原因**：Worker解析失败或局部重分段算法问题
**排查**：查看Console错误日志，检查Worker状态

### 4. 功能回归
**症状**：M1功能不正常
**原因**：M2修改影响了现有功能
**解决**：检查keyed-diff、防抖、性能监控是否正常

## 📊 验证报告模板

验证完成后，请记录以下信息：

### 基础功能
- [ ] 应用正常加载
- [ ] 性能监控面板显示
- [ ] Worker初始化状态：✅成功 / ⚠️失败

### 局部重分段
- [ ] 文档开头插入：✅局部 / ❌全量
- [ ] 文档中间编辑：✅局部 / ❌全量  
- [ ] 文档末尾添加：✅局部 / ❌全量

### Worker解析
- [ ] Worker正常工作：✅是 / ❌否
- [ ] 降级机制正常：✅是 / ❌否
- [ ] 大文档处理：✅正常 / ❌异常

### 性能指标
- 长任务时间：____ms (目标<50ms)
- 输入延迟：____ms (目标<150ms)
- 滚动FPS：____ (目标≥55)

### 问题记录
- 发现的问题：
- 错误日志：
- 建议改进：

---

## 🚀 下一步

验证完成后，根据结果决定：
1. **全部通过** → 继续实施自适应防抖和语义锚点滚动
2. **部分问题** → 修复问题后重新验证
3. **重大问题** → 回退到M1，重新设计M2方案

请在浏览器中按照此指南进行验证，并报告结果！
