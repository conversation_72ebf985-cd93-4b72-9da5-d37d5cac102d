/**
 * BoundaryIndex - 行首偏移表和位置转换工具
 * 用于实现稳定的段落ID生成和高效的位置计算
 */

import { murmurHash3 } from 'murmurhash3js-revisited';

export type Pos = { line: number; col: number };

export interface ContentSegment {
  id: string;
  content: string;
  hash: number; // 缓存的哈希值
  startOffset: number;
  element?: HTMLElement;
}

/**
 * 边界索引类 - 维护行首偏移表，支持高效的位置转换
 */
export class BoundaryIndex {
  private lineStart: Uint32Array;
  private content: string;

  constructor(content: string) {
    this.content = content;
    this.lineStart = this.buildLineStartArray(content);
  }

  /**
   * 构建行首偏移数组（前缀和）
   */
  private buildLineStartArray(content: string): Uint32Array {
    const lines = content.split('\n');
    const lineStart = new Uint32Array(lines.length + 1);
    let offset = 0;
    
    for (let i = 0; i < lines.length; i++) {
      lineStart[i] = offset;
      offset += lines[i].length + 1; // +1 for '\n'
    }
    
    // 最后一行的结束位置
    lineStart[lines.length] = Math.max(0, offset - 1);
    
    return lineStart;
  }

  /**
   * 将行列位置转换为字符偏移 - O(1)
   */
  toOffset(pos: Pos): number {
    if (pos.line >= this.lineStart.length - 1) {
      return this.lineStart[this.lineStart.length - 1];
    }
    return this.lineStart[pos.line] + pos.col;
  }

  /**
   * 将字符偏移转换为行列位置 - O(log N) 二分查找
   */
  toPos(offset: number): Pos {
    let left = 0;
    let right = this.lineStart.length - 1;
    
    while (left < right) {
      const mid = Math.floor((left + right + 1) / 2);
      if (this.lineStart[mid] <= offset) {
        left = mid;
      } else {
        right = mid - 1;
      }
    }
    
    return {
      line: left,
      col: offset - this.lineStart[left]
    };
  }

  /**
   * 基于 CodeMirror changes 增量更新行首偏移表
   * 实现增量更新，避免重新构建整个索引
   */
  applyChanges(changes: any): void {
    if (!changes) return;

    // 遍历所有变更片段
    changes.iterChanges((fromA: number, toA: number, fromB: number, toB: number, inserted: any) => {
      // fromA, toA: 旧文档中的范围
      // fromB, toB: 新文档中的范围
      // inserted: 插入的内容

      // 计算变更影响的行范围
      const startPos = this.toPos(fromA);
      const endPos = this.toPos(toA);

      // 计算插入内容的行数
      const insertedText = inserted.toString();
      const insertedLines = insertedText.split('\n');
      const insertedLineCount = insertedLines.length - 1; // 减1因为最后一行不增加行数

      // 计算删除的行数
      const deletedLineCount = endPos.line - startPos.line;

      // 计算行数变化量
      const lineDelta = insertedLineCount - deletedLineCount;

      // 如果没有行数变化，只需要更新受影响行的偏移
      if (lineDelta === 0) {
        // 计算字符数变化
        const deletedChars = toA - fromA;
        const insertedChars = insertedText.length;
        const charDelta = insertedChars - deletedChars;

        // 更新受影响行之后的所有行偏移
        for (let i = endPos.line + 1; i < this.lineStart.length; i++) {
          this.lineStart[i] += charDelta;
        }
      } else {
        // 有行数变化，需要重新构建受影响部分
        this.rebuildFromLine(startPos.line, insertedText, lineDelta);
      }
    });
  }

  /**
   * 从指定行开始重新构建行首偏移表
   */
  private rebuildFromLine(startLine: number, insertedText: string, lineDelta: number): void {
    // 如果行数发生变化，需要调整数组大小
    if (lineDelta !== 0) {
      const newLineStart = new Uint32Array(this.lineStart.length + lineDelta);

      // 复制变更前的部分
      for (let i = 0; i <= startLine; i++) {
        newLineStart[i] = this.lineStart[i];
      }

      // 重新计算插入部分的行偏移
      const insertedLines = insertedText.split('\n');
      let currentOffset = this.lineStart[startLine];

      for (let i = 0; i < insertedLines.length - 1; i++) {
        newLineStart[startLine + i + 1] = currentOffset + insertedLines[i].length + 1;
        currentOffset = newLineStart[startLine + i + 1];
      }

      // 计算后续行的偏移变化
      const totalCharDelta = insertedText.length - (this.lineStart[startLine + 1] - this.lineStart[startLine]);

      // 复制并调整后续行的偏移
      for (let i = startLine + insertedLines.length; i < newLineStart.length; i++) {
        const oldIndex = i - lineDelta;
        if (oldIndex < this.lineStart.length) {
          newLineStart[i] = this.lineStart[oldIndex] + totalCharDelta;
        }
      }

      this.lineStart = newLineStart;
    }
  }

  /**
   * 获取总行数
   */
  getLineCount(): number {
    return this.lineStart.length - 1;
  }

  /**
   * 获取指定行的起始偏移
   */
  getLineStart(line: number): number {
    if (line >= this.lineStart.length - 1) {
      return this.lineStart[this.lineStart.length - 1];
    }
    return this.lineStart[line];
  }
}

/**
 * 生成稳定的段落ID
 * 使用 MurmurHash3 x86_32 + startOffset 确保唯一性和稳定性
 */
export const generateStableSegmentId = (content: string, startOffset: number): string => {
  // 使用MurmurHash3 x86_32生成32位哈希
  const hash = murmurHash3.x86.hash32(content);
  return `${hash}:${startOffset}`;
};

/**
 * 快速哈希函数 - 用于内容变化检测
 */
export const fastHash32 = (content: string): number => {
  return murmurHash3.x86.hash32(content);
};

/**
 * 分割内容为段落，生成稳定ID
 */
export const splitContentWithStableIds = (content: string): ContentSegment[] => {
  const boundaryIndex = new BoundaryIndex(content);
  const lines = content.split('\n');
  const segments: ContentSegment[] = [];
  
  let currentSegment = '';
  let segmentStartLine = 0;
  let inCodeBlock = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测代码块边界
    if (line.trim().startsWith('```')) {
      inCodeBlock = !inCodeBlock;
      currentSegment += (currentSegment ? '\n' : '') + line;
      
      // 代码块结束时，创建段落
      if (!inCodeBlock && currentSegment.trim()) {
        const segmentContent = currentSegment.trim();
        const startOffset = boundaryIndex.getLineStart(segmentStartLine);
        const hash = fastHash32(segmentContent);
        
        segments.push({
          id: generateStableSegmentId(segmentContent, startOffset),
          content: segmentContent,
          hash,
          startOffset
        });
        
        currentSegment = '';
        segmentStartLine = i + 1;
      }
      continue;
    }

    // 在代码块内，直接添加行
    if (inCodeBlock) {
      currentSegment += (currentSegment ? '\n' : '') + line;
      continue;
    }

    // 空行分割段落
    if (line.trim() === '') {
      if (currentSegment.trim() !== '') {
        const segmentContent = currentSegment.trim();
        const startOffset = boundaryIndex.getLineStart(segmentStartLine);
        const hash = fastHash32(segmentContent);
        
        segments.push({
          id: generateStableSegmentId(segmentContent, startOffset),
          content: segmentContent,
          hash,
          startOffset
        });
        
        currentSegment = '';
        segmentStartLine = i + 1;
      }
    } else {
      currentSegment += (currentSegment ? '\n' : '') + line;
    }
  }

  // 处理最后一个段落
  if (currentSegment.trim() !== '') {
    const segmentContent = currentSegment.trim();
    const startOffset = boundaryIndex.getLineStart(segmentStartLine);
    const hash = fastHash32(segmentContent);
    
    segments.push({
      id: generateStableSegmentId(segmentContent, startOffset),
      content: segmentContent,
      hash,
      startOffset
    });
  }

  return segments;
};

/**
 * 防抖旁路判断 - 大块变更绕过防抖
 */
export const shouldBypassDebounce = (changeInfo: {
  lines?: number;
  bytes?: number;
}): boolean => {
  const LARGE_CHANGE_THRESHOLD = {
    lines: 200,
    bytes: 64 * 1024 // 64KB
  };

  return (changeInfo.lines && changeInfo.lines >= LARGE_CHANGE_THRESHOLD.lines) ||
         (changeInfo.bytes && changeInfo.bytes >= LARGE_CHANGE_THRESHOLD.bytes);
};

/**
 * 局部重分段核心钩子函数
 */

// 计算受影响的行范围
export const calculateAffectedLineRange = (
  changes: any,
  boundaryIndex: BoundaryIndex
): { start: number; end: number } => {
  let minLine = Infinity;
  let maxLine = -1;

  changes.iterChanges((fromA: number, toA: number) => {
    const startPos = boundaryIndex.toPos(fromA);
    const endPos = boundaryIndex.toPos(toA);

    minLine = Math.min(minLine, startPos.line);
    maxLine = Math.max(maxLine, endPos.line);
  });

  // 外扩1行作为缓冲，处理边界情况
  return {
    start: Math.max(0, minLine - 1),
    end: Math.min(boundaryIndex.getLineCount() - 1, maxLine + 1)
  };
};

// 找到受影响的段落索引范围
export const findAffectedSegments = (
  segments: ContentSegment[],
  lineRange: { start: number; end: number },
  boundaryIndex: BoundaryIndex
): { start: number; end: number } => {
  const startOffset = boundaryIndex.getLineStart(lineRange.start);
  const endOffset = boundaryIndex.getLineStart(lineRange.end);

  let startIndex = -1;
  let endIndex = -1;

  // 二分查找受影响的段落范围
  for (let i = 0; i < segments.length; i++) {
    const segment = segments[i];

    // 段落与受影响范围有交集
    if (segment.startOffset <= endOffset &&
        segment.startOffset + segment.content.length >= startOffset) {
      if (startIndex === -1) startIndex = i;
      endIndex = i;
    }
  }

  // 外扩1段作为缓冲
  return {
    start: Math.max(0, startIndex - 1),
    end: Math.min(segments.length - 1, endIndex + 1)
  };
};

// 对指定范围重新分段
export const splitContentInRange = (
  content: string,
  startOffset: number,
  endOffset: number,
  boundaryIndex: BoundaryIndex
): ContentSegment[] => {
  // 提取指定范围的内容
  const rangeContent = content.slice(startOffset, endOffset);

  // 使用现有的分段逻辑
  const tempSegments = splitContentWithStableIds(rangeContent);

  // 调整段落的startOffset
  return tempSegments.map(segment => ({
    ...segment,
    startOffset: segment.startOffset + startOffset,
    id: generateStableSegmentId(segment.content, segment.startOffset + startOffset)
  }));
};
