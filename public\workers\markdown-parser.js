/**
 * Markdown解析Worker - 简化版本
 * 暂时使用简单的Markdown解析，避免复杂依赖导致的加载问题
 */

// 简单的Markdown解析器（不依赖外部库）
function simpleMarkdownParser(content) {
  let html = content;

  // 处理标题
  html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
  html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
  html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

  // 处理粗体和斜体
  html = html.replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>');
  html = html.replace(/\*(.*)\*/gim, '<em>$1</em>');

  // 处理代码块
  html = html.replace(/```(\w+)?\n([\s\S]*?)```/gim, '<pre><code>$2</code></pre>');
  html = html.replace(/`([^`]+)`/gim, '<code>$1</code>');

  // 处理链接
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2" rel="noopener noreferrer">$1</a>');

  // 处理图片
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/gim, '<img src="$2" alt="$1" loading="lazy" width="auto" height="auto">');

  // 处理段落
  html = html.replace(/\n\n/gim, '</p><p>');
  html = '<p>' + html + '</p>';

  // 清理空段落
  html = html.replace(/<p><\/p>/gim, '');
  html = html.replace(/<p><h/gim, '<h');
  html = html.replace(/<\/h([1-6])><\/p>/gim, '</h$1>');
  html = html.replace(/<p><pre>/gim, '<pre>');
  html = html.replace(/<\/pre><\/p>/gim, '</pre>');

  return html;
}

// Worker协议接口
// 主线程 → Worker
// {
//   type: 'parse',
//   rev: number,
//   segments: Array<{ id: string, content: string }>
// }

// Worker → 主线程
// {
//   type: 'result',
//   rev: number,
//   results: Array<{ id: string, html: string, metadata?: object }>
// }

let currentRev = 0;

// 简化版本不需要复杂的处理器

// 处理单个段落
function processSegment(segment) {
  try {
    // 使用简单解析器
    let htmlString = simpleMarkdownParser(segment.content);

    // 提取元数据
    const metadata = extractMetadata(segment.content, htmlString);

    return {
      id: segment.id,
      html: htmlString,
      metadata
    };
  } catch (error) {
    console.error(`Segment processing error for ${segment.id}:`, error);
    return {
      id: segment.id,
      html: '<p>段落渲染错误</p>',
      metadata: { error: error.message }
    };
  }
}

// 这些函数已经集成到simpleMarkdownParser中，不再需要

// 提取元数据（TOC、标题等）
function extractMetadata(content, html) {
  const metadata = {};
  
  // 提取标题信息
  const headings = [];
  const headingRegex = /<(h[1-6])[^>]*id="([^"]*)"[^>]*>([^<]+)<\/h[1-6]>/g;
  let match;
  
  while ((match = headingRegex.exec(html)) !== null) {
    headings.push({
      level: parseInt(match[1].charAt(1)),
      id: match[2],
      text: match[3],
      tag: match[1]
    });
  }
  
  if (headings.length > 0) {
    metadata.headings = headings;
  }
  
  // 检查是否包含代码块
  if (content.includes('```')) {
    metadata.hasCodeBlocks = true;
  }
  
  // 检查是否包含表格
  if (content.includes('|')) {
    metadata.hasTables = true;
  }
  
  return metadata;
}

// 批量处理段落
function processSegments(segments) {
  const results = [];
  
  for (const segment of segments) {
    results.push(processSegment(segment));
  }
  
  return results;
}

// 监听主线程消息
self.onmessage = function(e) {
  const { type, rev, segments } = e.data;
  
  if (type !== 'parse') {
    console.warn('Unknown message type:', type);
    return;
  }
  
  // 检查版本号，避免处理陈旧请求
  if (rev < currentRev) {
    console.log(`Ignoring stale request: rev ${rev} < current ${currentRev}`);
    return;
  }
  
  currentRev = rev;
  
  try {
    console.log(`Worker: Processing ${segments.length} segments (rev ${rev})`);
    const startTime = performance.now();
    
    const results = processSegments(segments);
    
    const endTime = performance.now();
    console.log(`Worker: Processed ${segments.length} segments in ${endTime - startTime}ms`);
    
    // 发送结果回主线程
    self.postMessage({
      type: 'result',
      rev: rev,
      results: results
    });
    
  } catch (error) {
    console.error('Worker processing error:', error);
    
    // 发送错误信息
    self.postMessage({
      type: 'error',
      rev: rev,
      error: error.message
    });
  }
};

// Worker初始化完成
console.log('Markdown Parser Worker initialized');
self.postMessage({
  type: 'ready'
});
