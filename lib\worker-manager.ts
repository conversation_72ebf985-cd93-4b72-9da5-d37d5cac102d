/**
 * Worker管理器 - 处理主线程与Markdown解析Worker的通信
 */

import { type ContentSegment } from './boundary-index';

// Worker请求接口
interface WorkerRequest {
  type: 'parse';
  rev: number;
  segments: Array<{
    id: string;
    content: string;
  }>;
}

// Worker响应接口
interface WorkerResponse {
  type: 'result' | 'error' | 'ready';
  rev?: number;
  results?: Array<{
    id: string;
    html: string;
    metadata?: {
      headings?: Array<{
        level: number;
        id: string;
        text: string;
        tag: string;
      }>;
      hasCodeBlocks?: boolean;
      hasTables?: boolean;
      error?: string;
    };
  }>;
  error?: string;
}

// Worker管理器类
export class WorkerManager {
  private worker: Worker | null = null;
  private isReady = false;
  private currentRev = 0;
  private pendingCallbacks = new Map<number, {
    resolve: (results: any[]) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }>();
  
  // 初始化Worker
  async initialize(): Promise<void> {
    if (typeof Worker === 'undefined') {
      throw new Error('Web Workers not supported');
    }

    try {
      this.worker = new Worker('/workers/test-worker.js');

      this.worker.onmessage = (e: MessageEvent<WorkerResponse>) => {
        this.handleWorkerMessage(e.data);
      };

      this.worker.onerror = (error) => {
        this.handleWorkerError(new Error(`Worker error: ${error.message}`));
      };

      // 等待Worker准备就绪
      await this.waitForReady();

    } catch (error) {
      throw error;
    }
  }
  
  // 等待Worker准备就绪
  private waitForReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Worker initialization timeout'));
      }, 5000); // 5秒超时
      
      const checkReady = () => {
        if (this.isReady) {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };
      
      checkReady();
    });
  }
  
  // 处理Worker消息
  private handleWorkerMessage(data: WorkerResponse): void {
    switch (data.type) {
      case 'ready':
        this.isReady = true;
        break;
        
      case 'result':
        if (data.rev !== undefined && data.results) {
          this.handleParseResult(data.rev, data.results);
        }
        break;
        
      case 'error':
        if (data.rev !== undefined && data.error) {
          this.handleParseError(data.rev, new Error(data.error));
        }
        break;
        
      default:
        console.warn('Unknown worker message type:', data);
    }
  }
  
  // 处理解析结果
  private handleParseResult(rev: number, results: any[]): void {
    const callback = this.pendingCallbacks.get(rev);
    if (callback) {
      clearTimeout(callback.timeout);
      this.pendingCallbacks.delete(rev);
      callback.resolve(results);
    }
  }
  
  // 处理解析错误
  private handleParseError(rev: number, error: Error): void {
    const callback = this.pendingCallbacks.get(rev);
    if (callback) {
      clearTimeout(callback.timeout);
      this.pendingCallbacks.delete(rev);
      callback.reject(error);
    }
  }
  
  // 处理Worker错误
  private handleWorkerError(error: Error): void {
    // 拒绝所有待处理的请求
    for (const [rev, callback] of this.pendingCallbacks) {
      clearTimeout(callback.timeout);
      callback.reject(error);
    }
    this.pendingCallbacks.clear();
    
    // 标记Worker不可用
    this.isReady = false;
  }
  
  // 解析段落
  async parseSegments(segments: ContentSegment[]): Promise<Array<{
    id: string;
    html: string;
    metadata?: any;
  }>> {
    if (!this.worker || !this.isReady) {
      throw new Error('Worker not ready');
    }
    
    const rev = ++this.currentRev;
    
    return new Promise((resolve, reject) => {
      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingCallbacks.delete(rev);
        reject(new Error('Worker parse timeout'));
      }, 30000); // 30秒超时
      
      // 保存回调
      this.pendingCallbacks.set(rev, { resolve, reject, timeout });
      
      // 发送解析请求
      const request: WorkerRequest = {
        type: 'parse',
        rev,
        segments: segments.map(seg => ({
          id: seg.id,
          content: seg.content
        }))
      };
      
      this.worker!.postMessage(request);
    });
  }
  
  // 检查Worker是否可用
  isAvailable(): boolean {
    return this.worker !== null && this.isReady;
  }
  
  // 销毁Worker
  destroy(): void {
    if (this.worker) {
      // 清理所有待处理的回调
      for (const [rev, callback] of this.pendingCallbacks) {
        clearTimeout(callback.timeout);
        callback.reject(new Error('Worker destroyed'));
      }
      this.pendingCallbacks.clear();
      
      // 终止Worker
      this.worker.terminate();
      this.worker = null;
      this.isReady = false;
    }
  }
}

// 单例Worker管理器
let workerManager: WorkerManager | null = null;

// 获取Worker管理器实例
export const getWorkerManager = async (): Promise<WorkerManager> => {
  if (!workerManager) {
    workerManager = new WorkerManager();
    await workerManager.initialize();
  }
  return workerManager;
};

// 检查Worker是否支持
export const isWorkerSupported = (): boolean => {
  return typeof Worker !== 'undefined';
};

// 销毁Worker管理器
export const destroyWorkerManager = (): void => {
  if (workerManager) {
    workerManager.destroy();
    workerManager = null;
  }
};
