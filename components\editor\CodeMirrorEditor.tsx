'use client';

import React, { useEffect, useRef, useState, useMemo, useCallback } from 'react';
import { EditorView } from '@codemirror/view';
import { basicSetup } from 'codemirror';
import { EditorState } from '@codemirror/state';
import { markdown } from '@codemirror/lang-markdown';
import { oneDark } from '@codemirror/theme-one-dark';
import { useTheme } from 'next-themes';
import { shouldBypassDebounce } from '@/lib/boundary-index';

interface CodeMirrorEditorProps {
  value: string;
  onChange: (value: string, changes?: any) => void;
  placeholder?: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

export default function CodeMirrorEditor({
  value,
  onChange,
  placeholder = '开始编写您的 Markdown...',
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: CodeMirrorEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // 防抖机制 - 120ms固定防抖，支持旁路
  const debouncedOnChange = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    let lastChangeTime = 0;
    let lastChanges: any = null;

    return (newValue: string, changes?: any, changeInfo?: { lines: number; bytes: number }) => {
      const now = Date.now();
      lastChangeTime = now;
      lastChanges = changes;

      // 检查是否需要绕过防抖
      if (changeInfo && shouldBypassDebounce(changeInfo)) {
        clearTimeout(timeoutId);
        requestAnimationFrame(() => {
          onChange(newValue, lastChanges);
        });
        return;
      }

      // 正常防抖处理
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        // 确保这是最新的变更
        if (Date.now() - lastChangeTime >= 120) {
          requestAnimationFrame(() => {
            onChange(newValue, lastChanges);
          });
        }
      }, 120);
    };
  }, [onChange]);

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      // 组件卸载时清理定时器
    };
  }, []);

  // 跳转到指定行的函数
  const scrollToLine = (lineNumber: number) => {
    if (!viewRef.current) return;
    
    const view = viewRef.current;
    const line = Math.max(1, Math.min(lineNumber, view.state.doc.lines));
    const pos = view.state.doc.line(line).from;
    
    // 设置光标位置并滚动到可视区域
    view.dispatch({
      selection: { anchor: pos, head: pos },
      scrollIntoView: true
    });
    
    // 聚焦编辑器
    view.focus();
  };

  // 监听来自大纲面板的跳转事件
  useEffect(() => {
    const handleScrollToLine = (event: CustomEvent<{ line: number }>) => {
      scrollToLine(event.detail.line);
    };

    // 注册事件监听器
    document.addEventListener('scrollToLine', handleScrollToLine as EventListener);
    
    return () => {
      document.removeEventListener('scrollToLine', handleScrollToLine as EventListener);
    };
  }, []);

  useEffect(() => {
    if (!mounted || !editorRef.current) return;

    // 创建编辑器状态
    const state = EditorState.create({
      doc: value,
      extensions: [
        basicSetup,
        markdown(),
        theme === 'dark' ? oneDark : [],
        EditorView.updateListener.of((update) => {
          if (update.docChanged) {
            const newValue = update.state.doc.toString();

            // 计算变更信息用于防抖旁路判断
            const changeInfo = {
              lines: 0,
              bytes: newValue.length - value.length
            };

            // 计算变更的行数
            if (update.changes) {
              update.changes.iterChanges((fromA, toA, fromB, toB) => {
                const deletedLines = update.startState.doc.slice(fromA, toA).toString().split('\n').length - 1;
                const insertedLines = newValue.slice(fromB, toB).split('\n').length - 1;
                changeInfo.lines += Math.abs(insertedLines - deletedLines);
              });
            }

            // 使用防抖机制，传递changes信息
            debouncedOnChange(newValue, update.changes, changeInfo);
          }
          // 监听滚动变化 - 滚动不防抖，保持实时性
          if (update.viewportChanged && onScrollPositionChange) {
            const scrollTop = update.view.scrollDOM.scrollTop;
            onScrollPositionChange(scrollTop);
          }
        }),
        EditorView.theme({
          '&': {
            height: '100%',
            fontSize: '14px',
            fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace'
          },
          '.cm-content': {
            padding: '16px',
            minHeight: '100%',
            lineHeight: '1.6'
          },
          '.cm-focused': {
            outline: 'none'
          },
          '.cm-editor': {
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          },
          '.cm-scroller': {
            flex: '1',
            overflow: 'auto'
          },
          // 行号样式配置
          '.cm-gutters': {
            backgroundColor: theme === 'dark' ? '#21252b' : '#fafafa',
            borderRight: `1px solid ${theme === 'dark' ? '#3c4043' : '#e1e4e8'}`,
            color: theme === 'dark' ? '#6c7086' : '#656d76'
          },
          '.cm-lineNumbers .cm-gutterElement': {
            color: theme === 'dark' ? '#6c7086' : '#656d76',
            fontSize: '13px',
            minWidth: '3em',
            textAlign: 'right',
            paddingRight: '8px',
            paddingLeft: '4px'
          },
          '.cm-activeLineGutter': {
            backgroundColor: theme === 'dark' ? '#2c313c' : '#f6f8fa',
            color: theme === 'dark' ? '#ffffff' : '#24292f'
          },
          '.cm-activeLine': {
            backgroundColor: theme === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)'
          }
        }),
        EditorView.lineWrapping
      ]
    });

    // 创建编辑器视图
    const view = new EditorView({
      state,
      parent: editorRef.current
    });

    viewRef.current = view;

    // 恢复滚动位置
    if (initialScrollPosition !== undefined) {
      setTimeout(() => {
        if (view.scrollDOM) {
          view.scrollDOM.scrollTop = initialScrollPosition;
        }
      }, 100);
    }

    return () => {
      view.destroy();
      viewRef.current = null;
    };
  }, [mounted, theme]);

  // 当外部value变化时更新编辑器内容
  useEffect(() => {
    if (viewRef.current && value !== viewRef.current.state.doc.toString()) {
      const transaction = viewRef.current.state.update({
        changes: {
          from: 0,
          to: viewRef.current.state.doc.length,
          insert: value
        }
      });
      viewRef.current.dispatch(transaction);
    }
  }, [value]);

  if (!mounted) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="text-muted-foreground">加载编辑器...</div>
      </div>
    );
  }

  return (
    <div 
      ref={editorRef} 
      className={`w-full h-full ${className}`}
    />
  );
}
